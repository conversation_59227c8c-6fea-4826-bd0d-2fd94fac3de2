package com.siemens.spine.db.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @Date 2025/06/06
 */

@Entity
@Table(name = "query_template")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryTemplateEntity {

    @Id
    @GeneratedValue
    private Long id;

    @Column(name = "template_name", nullable = false, unique = true)
    private String templateName;

    @Column(name = "sql_template", nullable = false)
    private String sqlTemplate;

    @Column(name = "description")
    private String description;

    @Column(name = "requires_pagination", nullable = false)
    private boolean requiresPagination = false;

    @Column(name = "sys_create_date", updatable = false)
    @CreationTimestamp
    private Timestamp sysCreateDate;

    @Column(name = "sys_mod_date")
    @UpdateTimestamp
    private Timestamp sysModDate;

    @Column(name = "created_by", updatable = false)
    private String createdBy;

    @Column(name = "modified_by")
    private String modifiedBy;

}
