package com.siemens.spine.logic.service;

import com.siemens.spine.logic.dto.QueryTemplateDTO;
import com.siemens.spine.logic.dto.request.DynamicQueryByNameDTO;
import com.siemens.spine.logic.dto.request.DynamicQueryDTO;
import com.siemens.spine.logic.validator.query.ValidationResult;

import java.sql.SQLException;
import java.util.Collection;
import java.util.Map;

/**
 * Service for managing dynamic queries
 *
 * <AUTHOR> Le
 * @version 1.0
 * @since 24/2/2023
 */
public interface DynamicQueryService {

    /**
     * Get query template by id
     *
     * @param templateId template id
     * @return QueryTemplateDTO
     */
    QueryTemplateDTO getQueryTemplate(Long templateId);

    /**
     * Get query template by name
     *
     * @param templateName template name
     * @return QueryTemplateDTO
     */
    QueryTemplateDTO getQueryTemplate(String templateName);

    /**
     * Get total count of query result
     *
     * @param templateName template name
     * @param parameters   parameters
     * @return total count
     * @throws SQLException
     */
    int getTotalCount(String templateName, Map<String, Object> parameters) throws SQLException;

    /**
     * Execute query safely
     *
     * @param dto DynamicQueryDTO
     * @return query result
     * @throws SQLException
     */
    Map<String, Object> executeQuerySafe(DynamicQueryDTO dto) throws SQLException;

    /**
     * Execute query safely
     *
     * @param dto DynamicQueryByNameDTO
     * @return query result
     * @throws SQLException
     */
    Map<String, Object> executeQuerySafe(DynamicQueryByNameDTO dto) throws SQLException;

    /**
     * Get all query templates
     *
     * @return list of QueryTemplateDTO
     */
    Collection<QueryTemplateDTO> getAllTemplates(String s, String query, String name);

    /**
     * Validate query template
     *
     * @param template query template
     * @return ValidationResult
     * @throws SQLException
     */
    ValidationResult validateQueryTemplate(String template) throws SQLException;

    /**
     * Create query template
     *
     * @param template QueryTemplateDTO
     * @return QueryTemplateDTO
     * @throws SQLException
     */
    QueryTemplateDTO createQueryTemplate(QueryTemplateDTO template) throws SQLException;

    /**
     * Update query template
     *
     * @param template QueryTemplateDTO
     * @return QueryTemplateDTO
     * @throws SQLException
     */
    QueryTemplateDTO updateQueryTemplate(QueryTemplateDTO template) throws SQLException;

    /**
     * Delete query template
     *
     * @param templateId template id
     * @throws SQLException
     */
    void deleteQueryTemplate(Long templateId) throws SQLException;

}
