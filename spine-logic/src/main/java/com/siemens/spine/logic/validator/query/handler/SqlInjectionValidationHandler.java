package com.siemens.spine.logic.validator.query.handler;

import com.siemens.spine.logic.validator.query.QueryValidationContext;
import com.siemens.spine.logic.validator.query.ValidationResult;
import com.siemens.spine.logic.validator.query.visitor.DangerousPatternVisitor;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.Statements;
import net.sf.jsqlparser.statement.alter.Alter;
import net.sf.jsqlparser.statement.create.table.CreateTable;
import net.sf.jsqlparser.statement.drop.Drop;
import net.sf.jsqlparser.statement.truncate.Truncate;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Named;

@ApplicationScoped
@Named("sqlInjectionValidationHandler")
@Slf4j
public class SqlInjectionValidationHandler extends AbstractQueryValidationHandler {

    @Override
    protected ValidationResult doValidate(QueryValidationContext context) {
        try {
            Statement statement = context.getParsedStatement();
            if (statement == null) {
                statement = CCJSqlParserUtil.parse(context.getQueryString());
            }

            if (statement instanceof Drop ||
                    statement instanceof Alter ||
                    statement instanceof CreateTable ||
                    statement instanceof Truncate) {
                return ValidationResult.securityViolation("DDL statements are not allowed");
            }

            if (context.getQueryString().contains(";")) {
                Statements statements = CCJSqlParserUtil.parseStatements(context.getQueryString());
                if (statements.getStatements().size() > 1) {
                    return ValidationResult.securityViolation("Multiple statements are not allowed");
                }
            }

            DangerousPatternVisitor visitor = new DangerousPatternVisitor();
            statement.accept(visitor);

            if (visitor.isDangerous()) {
                return ValidationResult.securityViolation(visitor.getDangerReason());
            }

            return ValidationResult.success();

        } catch (JSQLParserException e) {
            log.error("Error parsing query: {}", e.getMessage());
            return ValidationResult.securityViolation("Query parsing failed - potential security risk");
        }
    }

}
