package com.siemens.spine.logic.validator.query.visitor;

import lombok.Getter;
import net.sf.jsqlparser.statement.StatementVisitorAdapter;
import net.sf.jsqlparser.statement.delete.Delete;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.update.Update;

@Getter
public class DangerousPatternVisitor extends StatementVisitorAdapter<Void> {

    private boolean dangerous = false;
    private String dangerReason = "";

    @Override
    public void visit(Select select) {
        // Check for dangerous functions
        FunctionCallVisitor functionVisitor = new FunctionCallVisitor();
        if (select.getSelectBody() != null) {
            select.getSelectBody().accept(functionVisitor);
            if (functionVisitor.hasDangerousFunctions()) {
                dangerous = true;
                dangerReason = "Query contains potentially dangerous functions: " + functionVisitor.getDangerousFunctions();
            }
        }
    }

    @Override
    public void visit(Update update) {
        if (update.getWhere() == null) {
            dangerous = true;
            dangerReason = "UPDATE without WHERE clause is not allowed";
        }
    }

    @Override
    public void visit(Delete delete) {
        // Check if delete has WHERE clause (prevent mass deletes)
        if (delete.getWhere() == null) {
            dangerous = true;
            dangerReason = "DELETE without WHERE clause is not allowed";
        }
    }

}